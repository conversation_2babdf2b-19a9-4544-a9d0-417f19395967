name: build
on: [push, pull_request]
jobs:
  build:
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [windows-latest, macos-latest, ubuntu-latest]
        build_type: [RelWithDebInfo, Debug]
        ui: [gui, cli]
        scripting: [lua, noscripts]
        exclude:
          - build_type: Debug
            ui: gui
          - build_type: RelWithDebInfo
            ui: cli
          - build_type: RelWithDebInfo
            scripting: noscripts
    steps:
    - uses: actions/checkout@v4
      with:
        submodules: 'recursive'
    - name: Install Dependencies
      if: runner.os == 'Linux'
      shell: bash
      run: |
        sudo apt-get update -qq
        sudo apt-get install -y \
          libpixman-1-dev libfreetype6-dev libharfbuzz-dev zlib1g-dev \
          libx11-dev libxcursor-dev libxi-dev libgl1-mesa-dev libfontconfig1-dev
    - name: Install Skia
      if: ${{ matrix.ui == 'gui' }}
      shell: bash
      run: |
        if [[ "${{ runner.os }}" == "Windows" ]] ; then
          this_dir=$(cygpath "${{ github.workspace }}")
        else
          this_dir="${{ github.workspace }}"
        fi
        skia_url=$(source $this_dir/laf/misc/skia-url.sh | xargs)
        skia_file=$(basename $skia_url)
        curl --ssl-revoke-best-effort -L -o "$skia_file" "$skia_url"
        unzip "$skia_file" -d skia
    - name: ccache
      uses: hendrikmuhs/ccache-action@v1.2.17
      if: ${{ runner.os == 'Linux' || runner.os == 'macOS' }}
      with:
        key: ${{ matrix.os }}-${{ matrix.ui }}-${{ matrix.scripting }}-${{ matrix.build_type }}
    - uses: aseprite/get-ninja@main
    - uses: ilammy/msvc-dev-cmd@v1
      if: runner.os == 'Windows'
    - name: Generating Makefiles
      shell: bash
      run: |
        if [[ "${{ runner.os }}" == "Windows" ]] ; then
          export enable_ccache=off
        else
          export enable_ccache=on
        fi

        if [[ "${{ matrix.ui }}" == "gui" ]] ; then
          export laf_backend=skia
        else
          export laf_backend=none
        fi

        if [[ "${{ matrix.scripting }}" == "lua" ]] ; then
          export enable_scripting=on
        else
          export enable_scripting=off
        fi

        if [[ "${{ runner.os }}" == "macOS" ]] ; then
          export skia_arch=arm64
        else
          export skia_arch=x64
        fi

        cmake -S . -B build -G Ninja \
          -DCMAKE_BUILD_TYPE=${{ matrix.build_type }} \
          -DCMAKE_OSX_DEPLOYMENT_TARGET=10.9 \
          -DENABLE_TESTS=ON \
          -DENABLE_SCRIPTING=$enable_scripting \
          -DENABLE_CCACHE=$enable_ccache \
          -DLAF_BACKEND=$laf_backend \
          -DSKIA_DIR=$(realpath skia) \
          -DSKIA_LIBRARY_DIR=$(realpath skia/out/Release-$skia_arch)
    - name: Compiling
      shell: bash
      run: |
        cd build && ninja
    - name: Running C++ Tests
      shell: bash
      run: |
        if [[ "${{ runner.os }}" == "Linux" ]] ; then
          export XVFB=xvfb-run
        fi
        cd build && $XVFB ctest --output-on-failure
    - name: Running CLI Tests
      if: ${{ matrix.scripting == 'lua' }}
      shell: bash
      run: |
        if [[ "${{ runner.os }}" == "Linux" ]] ; then
          export XVFB=xvfb-run
        fi
        export ASEPRITE=$PWD/build/bin/aseprite
        cd tests
        $XVFB bash run-tests.sh
