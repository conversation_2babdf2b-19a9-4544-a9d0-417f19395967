name: build
on: [push]
jobs:
  build:
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [windows-latest, macos-latest, ubuntu-latest]
    steps:
    - uses: actions/checkout@v4
    - uses: ilammy/msvc-dev-cmd@v1
      if: runner.os == 'Windows'
    - name: Generating Makefiles
      shell: bash
      run: |
        if [[ "${{ runner.os }}" == "Windows" ]] ; then
          cmake . -G "NMake Makefiles"
        else
          cmake . -G "Unix Makefiles"
        fi
    - name: Compiling
      run: cmake --build .
    - name: Running Tests
      shell: bash
      run: ctest --output-on-failure
