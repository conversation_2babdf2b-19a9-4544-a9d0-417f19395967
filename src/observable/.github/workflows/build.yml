name: build
on: [push]
jobs:
  build:
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [windows-2019, macos-latest, ubuntu-latest]
        build_type: [debug,release]
        benchmark: [false]
        sanitizer: [""]
        include:
          - os: macos-latest
            build_type: debug
            sanitizer: "-fsanitize=thread"
          - os: macos-latest
            build_type: debug
            sanitizer: "-fsanitize=address"
          - os: ubuntu-latest
            build_type: release
            benchmark: true
    steps:
    - uses: actions/checkout@v4
    - uses: aseprite/get-ninja@main
    - uses: ilammy/msvc-dev-cmd@v1
      if: runner.os == 'Windows'
    - name: Generating Makefiles
      shell: bash
      run: |
        cmake . -G Ninja \
          -DCMAKE_BUILD_TYPE=${{ matrix.build_type }} \
          -DCMAKE_CXX_FLAGS=${{ matrix.sanitizer }}  \
          -DCMAKE_EXE_LINKER_FLAGS=${{ matrix.sanitizer }} \
          -DOBSERVABLE_BENCHMARKS=${{ matrix.benchmark }}
    - name: Compiling
      run: cmake --build .
    - name: Running Tests
      shell: bash
      run: |
        ctest --output-on-failure
        if [[ "${{ matrix.benchmark }}" == "true" ]] ; then ./benchmarks/obs_benchmarks ; fi
