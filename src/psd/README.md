# Aseprite PSD Library

[![build](https://github.com/aseprite/psd/actions/workflows/build.yml/badge.svg)](https://github.com/aseprite/psd/actions/workflows/build.yml)
[![MIT Licensed](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE.txt)

Experimental and *Work-In-Progress* C++ library to read/write PSD files.

Based on [specs published by Adobe](https://www.adobe.com/devnet-apps/photoshop/fileformatashtml/)

## Roadmap

- [ ] Read PSD files
- [ ] Write PSD files
