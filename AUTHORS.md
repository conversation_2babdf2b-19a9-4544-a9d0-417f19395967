# Credits

Aseprite is being developed and maintained currently by [Igara Studio](https://igara.com/).
The active team of developers is:

* [<PERSON>](https://github.com/dacap)
* [<PERSON><PERSON>](https://github.com/Gasparoken)
* [<PERSON>](https://github.com/martin<PERSON><PERSON>)
* [<PERSON>](https://github.com/ckaiser)
* [<PERSON>](https://github.com/Liebranca)

Previous team members that contributed with code/docs/scripts/graphics:

* [<PERSON><PERSON><PERSON>](https://github.com/thkwznk)
* [<PERSON>](https://github.com/iamOgunyinka)
* [<PERSON>](https://github.com/dncampo)

## Translations

The translation work of Aseprite is only possible thanks to the
contribution, help, and good will of several translators coordinated
through our Weblate project:

* [Translation Credits](strings/README.md)

## Graphics

Aseprite logo was created by <PERSON>. Graphics used as background
of [Aseprite home page](https://www.aseprite.org),
on [Steam Store](https://store.steampowered.com/app/431730/Aseprite/),
and [social media channels](https://bsky.app/profile/aseprite.org),
were created by:

* [<PERSON>ija <PERSON>entijevic](https://ilkke.net/)

## Themes

The default Aseprite font was created by David Capello, and the
default Aseprite theme was introduced in v0.8, originally created by:

* Ilija Melentijevic

A modified dark version of this theme was introduced in v1.3-beta1, created by:

* [Nicolas Desilets](https://twitter.com/MapleGecko)

These themes are now being maintained by Igara Studio and external
contributors from time to time.

## Palettes

Aseprite includes color palettes created by:

* [Richard "DawnBringer" Fhager](http://pixeljoint.com/p/23821.htm), [DB16](http://pixeljoint.com/forum/forum_posts.asp?TID=12795), [DB32](http://pixeljoint.com/forum/forum_posts.asp?TID=16247) (default Aseprite color palette)
* [Arne Niklas Jansson](http://androidarts.com/), [16 colors](http://androidarts.com/palette/16pal.htm), [32 colors](http://wayofthepixel.net/index.php?topic=15824.msg144494)
* [ENDESGA Studios](https://twitter.com/ENDESGA), [EDG16 and EDG32](https://forums.tigsource.com/index.php?topic=46126.msg1279124#msg1279124), and [other palettes](https://twitter.com/ENDESGA/status/865812366931353600)
* [Hyohnoo Games](https://twitter.com/Hyohnoo), [mail24](https://twitter.com/Hyohnoo/status/797472587974639616) palette
* [Davit Masia](https://twitter.com/DavitMasia), [matriax8c](https://twitter.com/DavitMasia/status/834862452164612096) palette
* [Javier Guerrero](https://twitter.com/Xavier_Gd), [nyx8](https://twitter.com/Xavier_Gd/status/868519467864686594) palette
* [Adigun A. Polack](https://twitter.com/adigunpolack), [AAP-64](http://pixeljoint.com/pixelart/119466.htm), [AAP-Splendor128](http://pixeljoint.com/pixelart/120714.htm), [SimpleJPC-16](http://pixeljoint.com/pixelart/119844.htm), and [AAP-Micro12](http://pixeljoint.com/pixelart/121151.htm) palette
* [PineTreePizza](https://twitter.com/PineTreePizza), [Rosy-42](https://twitter.com/PineTreePizza/status/1006536191955623938) palette

## Pixel-art Features

Aseprite tries to replicate some pixel-art algorithms:

* [Shading Ink](https://aseprite.org/docs/shading/): created as a simplification of GrafX2 shade mode, thanks to Ilija Melentijevic for introducing me to this feature in 2009
* [RotSprite](http://forums.sonicretro.org/index.php?showtopic=8848&st=15&p=159754&#entry159754) by Xenowhirl.
* [Pixel perfect drawing algorithm](https://deepnight.net/blog/tools/pixel-perfect-drawing/)
  by [Sébastien Bénard](https://twitter.com/deepnightfr) and
  [Carduus](https://twitter.com/CarduusHimself/status/******************).

## Community

A special thanks to @Outlander for helping us moderating our [Discord server](https://discord.gg/Yb2CeX8).
Thanks to all the people that hung around for such a long time.

## Contributors

Thank you everyone who contributed to Aseprite with ideas, patches,
code, bug reports, new features, donations, tutorials, videos,
personal messages, chats, emails, tweets, posts, questions, libraries,
compilers, and any other tools that made this program possible today.

* Thanks to all [contributors](https://github.com/aseprite/aseprite/graphs/contributors)
* Thanks to all developers and maintainers behind [other open source projects](docs/LICENSES.md) used by Aseprite
* Thanks to all early PayPal donors and donors from our Pledgie Campaign (before Aseprite was commercialized)
* Thanks to every who support our business model: this source-available / sell-binaries combo
* Thanks to schools and [educational institutions](https://aseprite.org/educational)
  that are using Aseprite in their classrooms <3
* Thanks to our family and friends who always support our work

It's been more years than I can remember, sorry if we missed someone,
please drop me a line to [<EMAIL>](mailto:<EMAIL>) to
fix something or say hi. We'll try to keep this updated (for past and
future contributors).

Sincerely, David.
