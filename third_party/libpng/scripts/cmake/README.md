CMake Files, Lists and Scripts for the PNG Reference Library
============================================================

Copyright Notice
----------------

 * Copyright (c) 2018-2024 Co<PERSON>in <PERSON>.
 * Copyright (c) 2007-2018 <PERSON>.
 * Originally written by <PERSON>, 2007.

Use, modification and distribution of the CMake
files in the libpng distribution are subject to
the same licensing terms and conditions as libpng.
Please see the copyright notice in `png.h` or visit
http://libpng.org/pub/png/src/libpng-LICENSE.txt

File List
---------

    CMakeLists.txt                 ==>  The main CMake lists file
    scripts/cmake/AUTHORS.md       ==>  The Authors file
    scripts/cmake/README.md        ==>  This file
    scripts/cmake/PNGConfig.cmake  ==>  Config file for FindPNG
    scripts/cmake/genchk.cmake.in  ==>  Template for genchk.cmake
    scripts/cmake/genout.cmake.in  ==>  Template for genout.cmake
    scripts/cmake/gensrc.cmake.in  ==>  Template for gensrc.cmake
    scripts/cmake/test.cmake.in    ==>  Template for test.cmake

Acknowledgements
----------------

See the accompanying file `scripts/cmake/AUTHORS.md`
for the list of Contributing Authors.

If you are a Contributing Author, please make sure
that you are being acknowledged.
