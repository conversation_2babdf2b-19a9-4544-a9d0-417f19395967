name: windows
on:
  push:
    paths-ignore:
    - 'docs/**'
  pull_request:

jobs:
  windows:
    runs-on: windows-latest
    steps:
    - uses: actions/checkout@v1
    - uses: seanmiddleditch/gha-setup-vsdevenv@master
    - uses: seanmiddleditch/gha-setup-ninja@master
    - run: |
        mkdir build
        cd build
        cmake -GNinja -DCMAKE_CXX_COMPILER=cl.exe -DCMAKE_C_COMPILER=cl.exe -DUSE_WS=1 -DUSE_TEST=1 -DUSE_ZLIB=OFF -DBUILD_SHARED_LIBS=OFF ..
    - run: |
        cd build
        ninja
    - run: |
        cd build
        ninja test

#- run: ../build/test/ixwebsocket_unittest.exe
# working-directory: test
