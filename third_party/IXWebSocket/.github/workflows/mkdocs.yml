name: mkdocs
on:
  push:
    branches:
    - master
    paths:
    - 'docs/**'

jobs:
  linux:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Set up Python 3.8
      uses: actions/setup-python@v1
      with:
        python-version: 3.8
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install mkdocs
        pip install mkdocs-material
        pip install pygments
    - name: Build doc
      run: |
        git checkout master
        git clean -dfx .
        git fetch
        git pull
        mkdocs gh-deploy
