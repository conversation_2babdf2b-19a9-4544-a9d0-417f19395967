FROM centos:7 as build

RUN yum install -y gcc-c++ make zlib-devel openssl-devel redhat-rpm-config

RUN groupadd app && useradd -g app app
RUN chown -R app:app /opt
RUN chown -R app:app /usr/local

WORKDIR /tmp
RUN curl -O https://cmake.org/files/v3.14/cmake-3.14.0-Linux-x86_64.tar.gz
RUN tar zxvf cmake-3.14.0-Linux-x86_64.tar.gz
RUN cp -rf cmake-3.14.0-Linux-x86_64/* /usr/

RUN yum install -y git

# There is a bug in CMake where we cannot build from the root top folder
# So we build from /opt
COPY --chown=app:app . /opt
WORKDIR /opt

USER app
RUN [ "make", "ws_no_python" ]
RUN [ "rm", "-rf", "build" ]

ENTRYPOINT ["ws"]
CMD ["--help"]
