/*
 * SPDX-License-Identifier: BSD-2-Clause
 *
 * Copyright (c) 2023 <PERSON>
 * All rights reserved.
 */
#include "test.h"

/* Test L arg - make names lowercase */
DEFINE_TEST(test_L)
{
	const char *reffile = "test_basic.zip";
	int r;

	extract_reference_file(reffile);
	r = systemf("%s -L %s >test.out 2>test.err", testprog, reffile);
	assertEqualInt(0, r);
	assertNonEmptyFile("test.out");
	assertEmptyFile("test.err");

	assertTextFileContents("contents a\n", "test_basic/a");
	assertTextFileContents("contents b\n", "test_basic/b");
	assertTextFileContents("contents c\n", "test_basic/c");
	assertTextFileContents("contents CAPS\n", "test_basic/caps");
}
