.\"-
.\" SPDX-License-Identifier: BSD-2-Clause
.\"
.\" Copyright (c) 2007-2008 Dag-Erling Smørgrav
.\" All rights reserved.
.\"
.Dd June 27, 2023
.Dt BSDUNZIP 1
.Os
.Sh NAME
.Nm bsdunzip
.Nd extract files from a ZIP archive
.Sh SYNOPSIS
.Nm
.Op Fl aCcfjLlnopqtuvy
.Op { Fl O | Fl I No } Ar encoding
.Op Fl d Ar dir
.Op Fl x Ar pattern
.Op Fl P Ar password
.Ar zipfile
.Op Ar member ...
.Sh DESCRIPTION
.\" ...
The following options are available:
.Bl -tag -width Fl
.It Fl a
When extracting a text file, convert DOS-style line endings to
Unix-style line endings.
.It Fl C
Match file names case-insensitively.
.It Fl c
Extract to stdout/screen.
When extracting files from the zipfile, they are written to stdout.
This is similar to
.Fl p ,
but does not suppress normal output.
.It Fl d Ar dir
Extract files into the specified directory rather than the current
directory.
.It Fl f
Update existing.
Extract only files from the zipfile if a file with the same name
already exists on disk and is older than the former.
Otherwise, the file is silently skipped.
.It Fl I Ar encoding
.It Fl O Ar encoding
Convert filenames from the specified encoding.
.It Fl j
Ignore directories stored in the zipfile; instead, extract all files
directly into the extraction directory.
.It Fl L
Convert the names of the extracted files and directories to lowercase.
.It Fl l
List, rather than extract, the contents of the zipfile.
.It Fl n
No overwrite.
When extracting a file from the zipfile, if a file with the same name
already exists on disk, the file is silently skipped.
.It Fl o
Overwrite.
When extracting a file from the zipfile, if a file with the same name
already exists on disk, the existing file is replaced with the file
from the zipfile.
.It Fl p
Extract to stdout.
When extracting files from the zipfile, they are written to stdout.
The normal output is suppressed as if
.Fl q
was specified.
.It Fl P Ar password
Extract encrypted files using a password.
Putting a password on the command line using this option can be
insecure.
.It Fl q
Quiet: print less information while extracting.
.It Fl t
Test: do not extract anything, but verify the checksum of every file
in the archive.
.It Fl u
Update.
When extracting a file from the zipfile, if a file with the same name
already exists on disk, the existing file is replaced with the file
from the zipfile if and only if the latter is newer than the former.
Otherwise, the file is silently skipped.
.It Fl v
List verbosely, rather than extract, the contents of the zipfile.
This differs from
.Fl l
by using the long listing.
Note that most of the data is currently fake and does not reflect the
content of the archive.
.It Fl x Ar pattern
Exclude files matching the pattern
.Ar pattern .
.It Fl y
Print four digit years in listings instead of two.
.It Fl Z Ar mode
Emulate
.Xr zipinfo 1L
mode.
Enabling
.Xr zipinfo 1L
mode changes the way in which additional arguments are parsed.
Currently only
.Xr zipinfo 1L
mode 1 is supported, which lists the file names one per line.
.It Ar [member ...]
Optional list of members to extract from the zipfile.
Can include patterns, e.g.,
.Ar 'memberdir/*'
will extract all files and dirs below memberdir.
.El
.Pp
Note that only one of
.Fl n ,
.Fl o ,
and
.Fl u
may be specified.
If specified filename is
.Qq - ,
then data is read from
.Va stdin .
.Sh ENVIRONMENT
If the
.Ev UNZIP_DEBUG
environment variable is defined, the
.Fl q
command-line option has no effect, and additional debugging
information will be printed to
.Va stderr .
.Sh COMPATIBILITY
The
.Nm
utility aims to be sufficiently compatible with other implementations
to serve as a drop-in replacement in the context of the
.Xr ports 7
system.
No attempt has been made to replicate functionality which is not
required for that purpose.
.Pp
For compatibility reasons, command-line options will be recognized if
they are listed not only before but also after the name of the
zipfile.
.Pp
Normally, the
.Fl a
option should only affect files which are marked as text files in the
zipfile's central directory.
Since the
.Xr archive 3
library does not provide access to that information, it is not available
to the
.Nm
utility.
Instead, the
.Nm
utility will assume that a file is a text file if no non-ASCII
characters are present within the first block of data decompressed for
that file.
If non-ASCII characters appear in subsequent blocks of data, a warning
will be issued.
.Pp
The
.Nm
utility is only able to process ZIP archives handled by
.Xr libarchive 3 .
Depending on the installed version of
.Xr libarchive 3 ,
this may or may not include self-extracting or ZIPX archives.
.Sh SEE ALSO
.Xr libarchive 3
.Sh HISTORY
The
.Nm
utility appeared in
.Fx 8.0 .
.Sh AUTHORS
The
.Nm
utility and this manual page were written by
.An Dag-Erling Sm\(/orgrav <NAME_EMAIL> .
It uses the
.Xr archive 3
library developed by
.An Tim Kientzle <NAME_EMAIL> .
.Sh CAVEATS
The
.Nm
utility performs two scans of the command-line for arguments before
and after the archive name, so as to maintain compatibility with
Info-ZIP unzip.
As a result, the POSIX
.Ql --
double-dash string used to separate options from arguments will need to
be repeated.
For example, to extract a "-a.jpg" from "-b.zip" with overwrite, one
would need to invoke
.Dl bsdunzip -o -- -a.jpg -- -b.zip
