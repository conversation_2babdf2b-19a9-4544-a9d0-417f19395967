name: "CodeQL"

permissions:
  contents: read

on:
  push:
    branches: [ "master", "3.5" ]
  pull_request:
    branches: [ "master" ]
  schedule:
    - cron: "49 4 * * 2"

jobs:
  analyze:
    name: Analyze
    runs-on: ubuntu-latest
    permissions:
      actions: read
      security-events: write

    strategy:
      fail-fast: false
      matrix:
        language: [ cpp ]

    steps:
      - name: Checkout
        uses: actions/checkout@eef61447b9ff4aafe5dcd4e0bbf5d482be7e7871 # v4.2.1

      - name: Initialize CodeQL
        uses: github/codeql-action/init@c36620d31ac7c881962c3d9dd939c40ec9434f2b # v3.26.12
        with:
          languages: ${{ matrix.language }}
          queries: +security-and-quality

      - name: Autobuild
        uses: github/codeql-action/autobuild@c36620d31ac7c881962c3d9dd939c40ec9434f2b # v3.26.12

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@c36620d31ac7c881962c3d9dd939c40ec9434f2b # v3.26.12
        with:
          category: "/language:${{ matrix.language }}"
